package com.midas.crm.controller;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;
import com.midas.crm.service.AsesorService;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.utils.GenericResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${api.route.asesores}")
@RequiredArgsConstructor
@Slf4j
public class AsesorController {

    private final AsesorService asesorService;
    private final CoordinadorService coordinadorService;
    private final SimpMessagingTemplate messagingTemplate;
    /**
     * Obtiene todos los asesores
     * @return Lista de asesores
     */
    // Endpoint REST comentado - usar WebSocket en su lugar
    // @GetMapping
    // public ResponseEntity<List<AsesorDTO>> getAllAsesores() {
    //     log.info("Obteniendo todos los asesores");
    //     List<AsesorDTO> asesores = asesorService.getAllAsesores();
    //     return ResponseEntity.ok(asesores);
    // }

    /**
     * Obtiene un asesor por su ID
     * @param id ID del asesor
     * @return Asesor si existe
     */
    @GetMapping("/{id}")
    public ResponseEntity<AsesorDTO> getAsesorById(@PathVariable Long id) {
        //log.info("Obteniendo asesor con ID: {}", id);
        return asesorService.getAsesorById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene los asesores asignados a un coordinador
     * @param coordinadorId ID del coordinador
     * @return Lista de asesores
     */
    // Endpoint REST comentado - usar WebSocket en su lugar
    // @GetMapping("/por-coordinador/{coordinadorId}")
    // public ResponseEntity<List<AsesorDTO>> getAsesoresByCoordinador(@PathVariable Long coordinadorId) {
    //     log.info("Obteniendo asesores asignados al coordinador con ID: {}", coordinadorId);
    //     List<AsesorDTO> asesores = asesorService.getAsesoresByCoordinadorId(coordinadorId);
    //     return ResponseEntity.ok(asesores);
    // }

    /**
     * Obtiene los clientes residenciales asociados a un asesor
     * @param id ID del asesor
     * @return Lista de clientes residenciales
     */
    // Endpoint REST comentado - usar WebSocket en su lugar
    // @GetMapping("/{id}/clientes")
    // public ResponseEntity<List<ClienteResidencialDTO>> getClientesByAsesor(@PathVariable Long id) {
    //     log.info("Obteniendo clientes del asesor con ID: {}", id);
    //     return asesorService.getClientesByAsesor(id);
    // }

    /**
     * Obtiene estadísticas de clientes para un asesor
     * @param id ID del asesor
     * @return Estadísticas de clientes
     */
    @GetMapping("/{id}/estadisticas")
    public ResponseEntity<Map<String, Object>> getEstadisticasByAsesor(@PathVariable Long id) {
        //log.info("Obteniendo estadísticas para el asesor con ID: {}", id);
        return asesorService.getEstadisticasByAsesor(id);
    }

    /**
     * Obtiene los clientes con venta realizada asociados a un asesor
     * @param id ID del asesor
     * @return Lista de clientes con venta realizada
     */
    @GetMapping("/{id}/ventas")
    public ResponseEntity<List<ClienteResidencialDTO>> getVentasByAsesor(@PathVariable Long id) {
        //log.info("Obteniendo ventas realizadas por el asesor con ID: {}", id);
        return asesorService.getVentasByAsesor(id);
    }

    @GetMapping("/{coordinadorId}/asesores-con-clientes")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getClientesByCoordinador(
            @PathVariable Long coordinadorId,
            @RequestParam(required = false) String dni,
            @RequestParam(required = false) String nombre,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(required = false) String fecha,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        //log.info("Obteniendo clientes residenciales de los asesores asignados al coordinador con ID {}", coordinadorId);
        //log.debug("Filtros aplicados - DNI: {}, Nombre: {}, Móvil: {}, Fecha: {}, Página: {}, Tamaño: {}",
                //dni, nombre, numeroMovil, fecha, page, size);

        GenericResponse<Map<String, Object>> response = coordinadorService.obtenerClientesPorCoordinador(
                coordinadorId, dni, nombre, numeroMovil, fecha, page, size);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/exportar-hoy/coordinador/{coordinadorId}")
    public ResponseEntity<byte[]> exportarClientesHoyDeAsesores(@PathVariable Long coordinadorId) {
        return asesorService.exportarClientesHoyDeAsesores(coordinadorId);
    }

    // Implementación de WebSockets para operaciones de listado
    @MessageMapping("/asesores.list")
    @SendTo("/topic/asesores")
    public List<AsesorDTO> getAsesoresWs() {
        // log.info("Solicitud WebSocket para listar asesores");
        return asesorService.getAllAsesores();
    }

    @MessageMapping("/asesores.por-coordinador")
    @SendTo("/topic/asesores/por-coordinador")
    public List<AsesorDTO> getAsesoresByCoordinadorWs(Map<String, Object> payload) {
        Long coordinadorId = Long.valueOf(payload.get("coordinadorId").toString());
        // log.info("Solicitud WebSocket para listar asesores por coordinador: {}", coordinadorId);
        return asesorService.getAsesoresByCoordinadorId(coordinadorId);
    }

    @MessageMapping("/asesores.clientes")
    @SendTo("/topic/asesores/clientes")
    public List<ClienteResidencialDTO> getClientesByAsesorWs(Map<String, Object> payload) {
        Long asesorId = Long.valueOf(payload.get("asesorId").toString());
        // log.info("Solicitud WebSocket para listar clientes por asesor: {}", asesorId);
        return asesorService.getClientesByAsesor(asesorId).getBody();
    }
}