import {
  Component,
  OnInit,
  OnDestroy,
  Input,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil, debounceTime } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import {
  EstadisticasSedeService,
  EstadisticaSedePaginadaResponse,
} from '../../services/estadisticas-sede.service';
import { SedeService } from '@app/services/sede.service';
import { LeadsAsesorDialogComponent } from '../leads-asesor-dialog/leads-asesor-dialog.component';
import { ClienteConUsuarioDTO } from '@app/models/backend/clienteresidencial';
import { Store } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromClienteActions from '../../store/save/save.actions';
import Swal from 'sweetalert2';

export interface EstadisticaSede {
  sede: string;
  supervisor: string;
  vendedor: string;
  tomaDatos: number;
  interesadosSeguro: number;
  interesadosEnergia: number;
  interesadosLowi: number;
}

@Component({
  selector: 'app-graficos-sede',
  templateUrl: './graficos-sede.component.html',
  styleUrls: ['./graficos-sede.component.scss'],
})
export class GraficosSedeComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject<void>();

  // ViewChild para el paginador
  @ViewChild('estadisticasPaginator') paginator!: MatPaginator;

  // Función callback para ver detalles del cliente
  @Input() onVerDetallesCliente?: (cliente: ClienteConUsuarioDTO) => void;

  // Estado del diálogo de leads para poder reabrirlo
  private dialogoLeadsAbierto: {
    nombreAsesor: string;
    fecha: string;
  } | null = null;

  // Controles de formulario
  buscarVendedorControl = new FormControl('');
  sedeControl = new FormControl('todas');
  supervisorControl = new FormControl('todos');
  fechaInicioControl = new FormControl(new Date().toISOString().split('T')[0]);
  fechaFinControl = new FormControl('');

  // Datos
  sedes: any[] = [];
  supervisores: any[] = [];
  estadisticas: EstadisticaSede[] = [];
  estadisticasFiltradas: EstadisticaSede[] = [];
  loading = false;
  exportLoading = false;
  exportRangoLoading = false;

  // Paginación
  currentPage = 0;
  pageSize = 10;
  totalPages = 0;
  totalElements = 0;
  hasNext = false;
  hasPrevious = false;

  // Opciones de tamaño de página
  pageSizeOptions = [5, 10, 20, 50];

  // Configuración de tabla
  displayedColumns: string[] = [
    'sede',
    'supervisor',
    'vendedor',
    'tomaDatos',
    'interesadosSeguro',
    'interesadosEnergia',
    'interesadosLowi',
  ];

  constructor(
    private estadisticasSedeService: EstadisticasSedeService,
    private sedeService: SedeService,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private store: Store<fromRoot.State>
  ) {}

  ngOnInit(): void {
    // Inicializar datos filtrados
    this.estadisticasFiltradas = [];

    this.cargarSedes();
    this.configurarSuscripciones();
    this.cargarEstadisticas();
  }

  ngAfterViewInit(): void {
    // Sincronizar el paginador después de que se inicialice la vista
    // No necesitamos suscripción aquí porque manejamos la sincronización directamente
    // en el método cargarEstadisticas()
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private cargarSedes(): void {
    this.sedeService
      .getAllSedes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.sedes = response.data;
          }
        },
        error: (error) => {
          console.error('Error al cargar sedes:', error);
        },
      });
  }

  private cargarSupervisores(sedeId: number): void {
    this.estadisticasSedeService
      .obtenerSupervisoresPorSede(sedeId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.supervisores = response.data;
          } else {
            this.supervisores = [];
          }
          // Resetear el control de supervisor cuando cambian los supervisores
          this.supervisorControl.setValue('todos');
        },
        error: (error) => {
          console.error('Error al cargar supervisores:', error);
          this.supervisores = [];
          this.supervisorControl.setValue('todos');
        },
      });
  }

  private configurarSuscripciones(): void {
    // Escuchar cambios en el buscador de vendedor (ahora con backend)
    this.buscarVendedorControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((busqueda) => {
        console.log('Búsqueda de vendedor cambió a:', busqueda);
        // Resetear paginación y cargar datos del backend con la búsqueda
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en la sede para cargar supervisores
    this.sedeControl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((sedeValue) => {
        console.log('Sede cambió a:', sedeValue);
        if (sedeValue && sedeValue !== 'todas') {
          this.cargarSupervisores(Number(sedeValue));
        } else {
          this.supervisores = [];
          this.supervisorControl.setValue('todos', { emitEvent: false });
        }
        // Cargar estadísticas después de cambiar la sede
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en el supervisor
    this.supervisorControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((supervisorValue) => {
        console.log('Supervisor cambió a:', supervisorValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en la fecha inicio
    this.fechaInicioControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((fechaValue) => {
        console.log('Fecha Inicio cambió a:', fechaValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en la fecha fin
    this.fechaFinControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((fechaValue) => {
        console.log('Fecha Fin cambió a:', fechaValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });
  }

  private cargarEstadisticas(): void {
    this.loading = true;
    const sedeIdValue = this.sedeControl.value;
    const sedeId = sedeIdValue === 'todas' ? null : Number(sedeIdValue);
    const supervisorIdValue = this.supervisorControl.value;
    const supervisorId =
      supervisorIdValue === 'todos' ? null : Number(supervisorIdValue);
    const fechaInicio = this.fechaInicioControl.value || '';
    const fechaFin = this.fechaFinControl.value || '';

    console.log('=== CARGANDO ESTADÍSTICAS ===');
    console.log('SedeId:', sedeId, '(valor original:', sedeIdValue, ')');
    console.log(
      'SupervisorId:',
      supervisorId,
      '(valor original:',
      supervisorIdValue,
      ')'
    );
    console.log('Tipo de supervisorId:', typeof supervisorId);
    console.log('Supervisores disponibles:', this.supervisores);
    console.log('Fecha Inicio:', fechaInicio);
    console.log('Fecha Fin:', fechaFin);
    console.log('Página:', this.currentPage);
    console.log('Tamaño:', this.pageSize);

    // Determinar si es rango de fechas o fecha específica
    console.log('=== DETECCIÓN DE RANGO DE FECHAS ===');
    console.log('fechaInicio:', fechaInicio);
    console.log('fechaFin:', fechaFin);
    console.log('fechaFin existe:', !!fechaFin);
    console.log('fechaFin !== fechaInicio:', fechaFin !== fechaInicio);

    const esRangoFechas = fechaFin && fechaFin !== fechaInicio;
    console.log('esRangoFechas:', esRangoFechas);

    if (esRangoFechas) {
      console.log(
        '=== USANDO ESTADÍSTICAS POR RANGO DE FECHAS (ACUMULADAS) ==='
      );
      console.log('Llamando a cargarEstadisticasPorRango...');
      this.cargarEstadisticasPorRango(
        sedeId,
        supervisorId,
        fechaInicio,
        fechaFin
      );
    } else {
      console.log('=== USANDO ESTADÍSTICAS POR FECHA ESPECÍFICA ===');
      console.log('Llamando a cargarEstadisticasPorFecha...');
      this.cargarEstadisticasPorFecha(sedeId, supervisorId, fechaInicio);
    }
  }

  private cargarEstadisticasPorFecha(
    sedeId: number | null,
    supervisorId: number | null,
    fecha: string
  ): void {
    // Obtener el término de búsqueda actual
    const busquedaVendedor = this.buscarVendedorControl.value?.trim() || null;

    // Usar el método con búsqueda si hay término de búsqueda, sino usar el método normal
    const serviceCall = busquedaVendedor
      ? this.estadisticasSedeService.obtenerEstadisticasPorSedePaginadoConBusqueda(
          sedeId,
          supervisorId,
          fecha,
          busquedaVendedor,
          this.currentPage,
          this.pageSize
        )
      : this.estadisticasSedeService.obtenerEstadisticasPorSedePaginado(
          sedeId,
          supervisorId,
          fecha,
          this.currentPage,
          this.pageSize
        );

    serviceCall.pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.procesarRespuestaEstadisticas(response);
      },
      error: (error) => {
        this.manejarErrorEstadisticas(error);
      },
      complete: () => {
        this.loading = false;
      },
    });
  }

  private cargarEstadisticasPorRango(
    sedeId: number | null,
    supervisorId: number | null,
    fechaInicio: string,
    fechaFin: string
  ): void {
    console.log('=== DENTRO DE cargarEstadisticasPorRango ===');
    console.log('sedeId:', sedeId);
    console.log('supervisorId:', supervisorId);
    console.log('fechaInicio:', fechaInicio);
    console.log('fechaFin:', fechaFin);
    console.log('currentPage:', this.currentPage);
    console.log('pageSize:', this.pageSize);

    // Obtener el término de búsqueda actual
    const busquedaVendedor = this.buscarVendedorControl.value?.trim() || null;
    console.log('busquedaVendedor:', busquedaVendedor);

    // Usar el método con búsqueda si hay término de búsqueda, sino usar el método normal
    const observable = busquedaVendedor
      ? this.estadisticasSedeService.obtenerEstadisticasPorRangoFechasConBusqueda(
          sedeId,
          supervisorId,
          fechaInicio,
          fechaFin,
          busquedaVendedor,
          this.currentPage,
          this.pageSize
        )
      : this.estadisticasSedeService.obtenerEstadisticasPorRangoFechas(
          sedeId,
          supervisorId,
          fechaInicio,
          fechaFin,
          this.currentPage,
          this.pageSize
        );

    observable.pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        console.log('=== RESPUESTA DE RANGO RECIBIDA ===');
        console.log('response:', response);
        this.procesarRespuestaEstadisticas(response);
      },
      error: (error) => {
        console.error('=== ERROR EN RANGO ===');
        console.error('error:', error);
        this.manejarErrorEstadisticas(error);
      },
      complete: () => {
        console.log('=== RANGO COMPLETADO ===');
        this.loading = false;
      },
    });
  }

  private procesarRespuestaEstadisticas(response: any): void {
    console.log('Respuesta del backend:', response);
    if (response.rpta === 1 && response.data) {
      const data: EstadisticaSedePaginadaResponse = response.data;
      console.log('Datos recibidos:', data);
      console.log(
        'Backend dice currentPage:',
        data.currentPage,
        'Frontend tiene currentPage:',
        this.currentPage
      );

      this.estadisticas = data.estadisticas;
      this.currentPage = data.currentPage;
      this.totalPages = data.totalPages;
      this.totalElements = data.totalElements;
      this.pageSize = data.pageSize;
      this.hasNext = data.hasNext;
      this.hasPrevious = data.hasPrevious;

      // Ya no necesitamos filtrar en frontend porque el backend ya lo hace
      // Los datos vienen filtrados desde el backend
      this.estadisticasFiltradas = [...this.estadisticas];

      // Sincronizar solo los datos necesarios, NO el pageIndex
      // El pageIndex se maneja a través del binding [pageIndex]="currentPage"
      console.log(
        'Datos sincronizados - currentPage:',
        this.currentPage,
        'totalElements:',
        this.totalElements
      );

      console.log(
        'Estado actualizado - Página:',
        this.currentPage,
        'Total páginas:',
        this.totalPages,
        'Total elementos:',
        this.totalElements
      );
    } else {
      this.resetearDatos();
    }
  }

  private manejarErrorEstadisticas(error: any): void {
    console.error('Error al cargar estadísticas:', error);
    this.resetearDatos();
  }

  // ===== MÉTODOS DE PAGINACIÓN =====

  private resetearPaginacion(): void {
    console.log('Reseteando paginación a página 0');
    this.currentPage = 0;
    // Resetear también el paginador visual de manera más robusta
    if (this.paginator) {
      // Usar setTimeout para asegurar que el reset se aplique correctamente
      setTimeout(() => {
        if (this.paginator) {
          this.paginator.pageIndex = 0;
          this.paginator.firstPage();
          this.cdr.detectChanges();
          console.log('Paginador reseteado a página 0');
        }
      }, 0);
    }
  }

  private resetearDatos(): void {
    this.estadisticas = [];
    this.estadisticasFiltradas = [];
    this.currentPage = 0;
    this.totalPages = 0;
    this.totalElements = 0;
    this.hasNext = false;
    this.hasPrevious = false;

    // Resetear también el paginador visual
    if (this.paginator) {
      this.paginator.pageIndex = 0;
      this.paginator.length = 0;
    }
  }

  // ===== MÉTODOS DE FILTRADO (AHORA EN BACKEND) =====
  // El filtrado por nombre de vendedor ahora se hace en el backend
  // Los datos vienen ya filtrados desde el servidor

  /**
   * Limpia la búsqueda de vendedor
   */
  limpiarBusquedaVendedor(): void {
    console.log('Limpiando búsqueda de vendedor');
    this.buscarVendedorControl.setValue('');
    // Ya no necesitamos forzar filtrado porque el valueChanges se encarga de recargar datos
  }

  /**
   * Método de debug para verificar el estado del buscador
   */
  debugBuscador(): void {
    console.log('=== DEBUG BUSCADOR ===');
    console.log(
      'buscarVendedorControl.value:',
      this.buscarVendedorControl.value
    );
    console.log('estadisticas.length:', this.estadisticas?.length || 0);
    console.log(
      'estadisticasFiltradas.length:',
      this.estadisticasFiltradas?.length || 0
    );
    console.log('estadisticas:', this.estadisticas);
    console.log('estadisticasFiltradas:', this.estadisticasFiltradas);
  }

  onPageChange(page: number): void {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
      this.cargarEstadisticas();
    }
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.resetearPaginacion();
    this.cargarEstadisticas();
  }

  // Método para manejar eventos de paginación de mat-paginator
  handlePageEvent(event: { pageIndex: number; pageSize: number }): void {
    console.log('=== HANDLE PAGE EVENT ===');
    console.log(
      'event.pageIndex:',
      event.pageIndex,
      'event.pageSize:',
      event.pageSize
    );
    console.log('currentPage ANTES:', this.currentPage);

    // Solo proceder si realmente cambió la página
    if (
      this.currentPage !== event.pageIndex ||
      this.pageSize !== event.pageSize
    ) {
      console.log('Página cambió de', this.currentPage, 'a', event.pageIndex);

      // Actualizar las propiedades
      this.currentPage = event.pageIndex;
      this.pageSize = event.pageSize;

      console.log('currentPage DESPUÉS:', this.currentPage);

      // Cargar estadísticas con los nuevos valores
      this.cargarEstadisticas();
    } else {
      console.log('Página no cambió, ignorando evento');
    }
  }

  // ===== MÉTODOS PARA MANEJAR LEADS =====

  /**
   * Abre el diálogo para mostrar los leads de un asesor específico
   * @param estadistica Estadística del asesor seleccionado
   */
  verLeadsAsesor(estadistica: EstadisticaSede): void {
    // Obtener las fechas de los controles
    const fechaInicio =
      this.fechaInicioControl.value || new Date().toISOString().split('T')[0];
    const fechaFin = this.fechaFinControl.value || '';

    console.log('=== VER LEADS ASESOR ===');
    console.log('Fecha Inicio:', fechaInicio);
    console.log('Fecha Fin:', fechaFin);
    console.log('Es rango de fechas:', fechaFin && fechaFin !== fechaInicio);

    // Guardar el estado del diálogo para poder reabrirlo
    this.dialogoLeadsAbierto = {
      nombreAsesor: estadistica.vendedor,
      fecha: fechaInicio,
    };

    // Preparar los datos para el modal
    const modalData: any = {
      nombreAsesor: estadistica.vendedor,
      fecha: fechaInicio,
      onVerDetalles: this.crearCallbackVerDetalles(), // Usar callback personalizado
    };

    // Si hay fecha fin y es diferente a la fecha inicio, agregar fecha fin
    if (fechaFin && fechaFin !== fechaInicio) {
      modalData.fechaFin = fechaFin;
    }

    // Abrir el diálogo con los datos del asesor
    const dialogRef = this.dialog.open(LeadsAsesorDialogComponent, {
      width: '90vw',
      maxWidth: '1200px',
      height: '80vh',
      data: modalData,
    });

    // Manejar el cierre del diálogo
    dialogRef.afterClosed().subscribe((result) => {
      console.log('Diálogo de leads cerrado con resultado:', result);
      // Solo limpiar el estado si no se cerró para abrir el modal de detalles
      if (result !== 'verDetalles') {
        this.dialogoLeadsAbierto = null;
        console.log('Estado dialogoLeadsAbierto limpiado');
      } else {
        console.log('Estado dialogoLeadsAbierto mantenido para reapertura');
      }
    });
  }

  /**
   * Crea un callback personalizado que guarda el estado y llama al callback del padre
   */
  private crearCallbackVerDetalles(): (cliente: ClienteConUsuarioDTO) => void {
    return (cliente: ClienteConUsuarioDTO) => {
      console.log('Callback verDetalles ejecutado con cliente:', cliente);

      // Llamar al callback del componente padre si existe
      if (this.onVerDetallesCliente) {
        console.log(
          'Llamando al callback del componente padre onVerDetallesCliente'
        );
        this.onVerDetallesCliente(cliente);
      } else {
        console.log('No hay callback del componente padre definido');
        // Si no hay callback del padre, podemos manejar los detalles aquí directamente
        // usando el store para cargar los detalles del cliente

        // Formatear la fecha según el tipo de dato
        let fechaCreacion = cliente.getFechaCreacionFormatted();

        // No necesitamos importar las acciones aquí, ya están importadas a nivel de clase

        // Usar el store para cargar los detalles del cliente
        this.store.dispatch(
          fromClienteActions.loadClienteDetalle({
            dni: cliente.dni,
            mobile: cliente.numeroMovil,
            fechaCreacion: fechaCreacion || '',
          })
        );
      }
    };
  }

  /**
   * Reabre el diálogo de leads del asesor
   */
  public reabrirDialogoLeads(): void {
    console.log('reabrirDialogoLeads() llamado');
    console.log('Estado dialogoLeadsAbierto:', this.dialogoLeadsAbierto);

    if (this.dialogoLeadsAbierto) {
      console.log(
        'Reabriendo diálogo de leads para:',
        this.dialogoLeadsAbierto.nombreAsesor,
        'fecha:',
        this.dialogoLeadsAbierto.fecha
      );

      // Obtener las fechas actuales de los controles para el rango
      const fechaInicio = this.dialogoLeadsAbierto.fecha;
      const fechaFin = this.fechaFinControl.value || '';

      // Preparar los datos para el modal
      const modalData: any = {
        nombreAsesor: this.dialogoLeadsAbierto.nombreAsesor,
        fecha: fechaInicio,
        onVerDetalles: this.crearCallbackVerDetalles(),
      };

      // Si hay fecha fin y es diferente a la fecha inicio, agregar fecha fin
      if (fechaFin && fechaFin !== fechaInicio) {
        modalData.fechaFin = fechaFin;
      }

      const dialogRef = this.dialog.open(LeadsAsesorDialogComponent, {
        width: '90vw',
        maxWidth: '1200px',
        height: '80vh',
        data: modalData,
      });

      // Manejar el cierre del diálogo
      dialogRef.afterClosed().subscribe((result) => {
        console.log(
          'Diálogo de leads reabierto y cerrado con resultado:',
          result
        );
        // Solo limpiar el estado si no se cerró para abrir el modal de detalles
        if (result !== 'verDetalles') {
          this.dialogoLeadsAbierto = null;
          console.log(
            'Estado dialogoLeadsAbierto limpiado después de reapertura'
          );
        } else {
          console.log(
            'Estado dialogoLeadsAbierto mantenido después de reapertura'
          );
        }
      });
    } else {
      console.log('No hay diálogo de leads para reabrir');
    }
  }

  /**
   * Exporta todos los leads filtrados por sede, supervisor y fecha a Excel
   * Usa el endpoint filtrado si hay búsqueda por vendedor aplicada
   */
  exportarPorFecha(): void {
    this.exportLoading = true;

    const sedeIdValue = this.sedeControl.value;
    const sedeId = sedeIdValue === 'todas' ? null : Number(sedeIdValue);
    const supervisorIdValue = this.supervisorControl.value;
    const supervisorId =
      supervisorIdValue === 'todos' ? null : Number(supervisorIdValue);
    const fecha =
      this.fechaInicioControl.value || new Date().toISOString().split('T')[0];
    const busquedaVendedor = this.buscarVendedorControl.value?.trim() || '';

    console.log('=== EXPORTAR POR FECHA ===');
    console.log('SedeId:', sedeId);
    console.log('SupervisorId:', supervisorId);
    console.log('Fecha:', fecha);
    console.log('Búsqueda Vendedor:', busquedaVendedor);

    // Decidir qué endpoint usar basado en si hay filtro de vendedor
    const exportObservable = busquedaVendedor
      ? this.estadisticasSedeService.exportarLeadsFiltrados(
          sedeId,
          supervisorId,
          fecha,
          null,
          null,
          busquedaVendedor
        )
      : this.estadisticasSedeService.exportarLeadsPorFecha(
          sedeId,
          supervisorId,
          fecha
        );

    exportObservable.pipe(takeUntil(this.destroy$)).subscribe({
      next: (blob) => {
        console.log('Excel generado exitosamente');

        // Crear nombre del archivo basado en los filtros
        let nombreArchivo = busquedaVendedor
          ? `leads_filtrados_${fecha}`
          : `leads_estadisticas_${fecha}`;
        if (sedeId !== null) {
          const sedeNombre =
            this.sedes.find((s) => s.id === sedeId)?.nombre || sedeId;
          nombreArchivo += `_sede_${sedeNombre}`;
        }
        if (supervisorId !== null) {
          const supervisorNombre =
            this.supervisores.find((s) => s.id === supervisorId)?.nombre ||
            supervisorId;
          nombreArchivo += `_supervisor_${supervisorNombre}`;
        }
        if (busquedaVendedor) {
          nombreArchivo += `_vendedor_${busquedaVendedor.replace(
            /[^a-zA-Z0-9]/g,
            '_'
          )}`;
        }
        nombreArchivo += '.xlsx';

        // Descargar el archivo
        this.downloadFile(blob, nombreArchivo);
        this.exportLoading = false;

        // Mostrar mensaje de éxito
        Swal.fire({
          title: 'Exportación exitosa',
          text: 'El archivo Excel se ha descargado correctamente',
          icon: 'success',
          timer: 3000,
          showConfirmButton: false,
        });
      },
      error: (error) => {
        console.error('Error al exportar leads:', error);
        this.exportLoading = false;

        // Mostrar mensaje de error
        Swal.fire({
          title: 'Error al exportar',
          text: 'No se pudo generar el archivo Excel. Por favor, inténtelo de nuevo.',
          icon: 'error',
          confirmButtonText: 'Entendido',
        });
      },
    });
  }

  /**
   * Exporta todos los leads filtrados por sede, supervisor y rango de fechas a Excel
   * Usa el endpoint filtrado si hay búsqueda por vendedor aplicada
   */
  exportarPorRangoFechas(): void {
    // Validar que ambas fechas estén seleccionadas
    if (!this.fechaInicioControl.value || !this.fechaFinControl.value) {
      Swal.fire({
        title: 'Fechas requeridas',
        text: 'Por favor, seleccione tanto la fecha de inicio como la fecha de fin.',
        icon: 'warning',
        confirmButtonText: 'Entendido',
      });
      return;
    }

    // Validar que la fecha de inicio no sea posterior a la fecha de fin
    const fechaInicio = new Date(this.fechaInicioControl.value);
    const fechaFin = new Date(this.fechaFinControl.value);

    if (fechaInicio > fechaFin) {
      Swal.fire({
        title: 'Fechas inválidas',
        text: 'La fecha de inicio no puede ser posterior a la fecha de fin.',
        icon: 'warning',
        confirmButtonText: 'Entendido',
      });
      return;
    }

    this.exportRangoLoading = true;

    const sedeIdValue = this.sedeControl.value;
    const sedeId = sedeIdValue === 'todas' ? null : Number(sedeIdValue);
    const supervisorIdValue = this.supervisorControl.value;
    const supervisorId =
      supervisorIdValue === 'todos' ? null : Number(supervisorIdValue);
    const fechaInicioStr = this.fechaInicioControl.value;
    const fechaFinStr = this.fechaFinControl.value;
    const busquedaVendedor = this.buscarVendedorControl.value?.trim() || '';

    console.log('=== EXPORTAR POR RANGO DE FECHAS ===');
    console.log('SedeId:', sedeId);
    console.log('SupervisorId:', supervisorId);
    console.log('Fecha Inicio:', fechaInicioStr);
    console.log('Fecha Fin:', fechaFinStr);
    console.log('Búsqueda Vendedor:', busquedaVendedor);

    // Decidir qué endpoint usar basado en si hay filtro de vendedor
    const exportObservable = busquedaVendedor
      ? this.estadisticasSedeService.exportarLeadsFiltrados(
          sedeId,
          supervisorId,
          null,
          fechaInicioStr,
          fechaFinStr,
          busquedaVendedor
        )
      : this.estadisticasSedeService.exportarLeadsPorRangoFechas(
          sedeId,
          supervisorId,
          fechaInicioStr,
          fechaFinStr
        );

    exportObservable.pipe(takeUntil(this.destroy$)).subscribe({
      next: (blob) => {
        console.log('Excel de rango generado exitosamente');

        // Crear nombre del archivo basado en los filtros
        let nombreArchivo = busquedaVendedor
          ? `leads_filtrados_${fechaInicioStr}_a_${fechaFinStr}`
          : `leads_estadisticas_${fechaInicioStr}_a_${fechaFinStr}`;
        if (sedeId !== null) {
          const sedeNombre =
            this.sedes.find((s) => s.id === sedeId)?.nombre || sedeId;
          nombreArchivo += `_sede_${sedeNombre}`;
        }
        if (supervisorId !== null) {
          const supervisorNombre =
            this.supervisores.find((s) => s.id === supervisorId)?.nombre ||
            supervisorId;
          nombreArchivo += `_supervisor_${supervisorNombre}`;
        }
        if (busquedaVendedor) {
          nombreArchivo += `_vendedor_${busquedaVendedor.replace(
            /[^a-zA-Z0-9]/g,
            '_'
          )}`;
        }
        nombreArchivo += '.xlsx';

        // Descargar el archivo
        this.downloadFile(blob, nombreArchivo);
        this.exportRangoLoading = false;

        // Mostrar mensaje de éxito
        Swal.fire({
          title: 'Exportación exitosa',
          text: 'El archivo Excel del rango de fechas se ha descargado correctamente',
          icon: 'success',
          timer: 3000,
          showConfirmButton: false,
        });
      },
      error: (error) => {
        console.error('Error al exportar leads por rango:', error);
        this.exportRangoLoading = false;

        // Mostrar mensaje de error
        Swal.fire({
          title: 'Error al exportar',
          text: 'No se pudo generar el archivo Excel del rango de fechas. Por favor, inténtelo de nuevo.',
          icon: 'error',
          confirmButtonText: 'Entendido',
        });
      },
    });
  }

  /**
   * Descarga un archivo blob
   */
  private downloadFile(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
