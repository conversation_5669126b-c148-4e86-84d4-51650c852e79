import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, ViewChild } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { Observable, Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import * as fromActions from '@app/store/user/user.actions';
import { UserPageResponse } from '@app/store/user/user.models';
import { State } from '@app/store/index';
import {
  getPaginatedUsers,
  getLoadingPage,
  getErrorPage,
  getLoading,
} from '@app/store/user/user.selectors';
import { User } from '@app/models/backend/user';
import { MatDialog } from '@angular/material/dialog';
import { RegistrationIndividualComponent } from '@app/pages/auth/pages/registrationindividual/registrationindividual.component';
import { EditUserDialogComponent } from './EditUserDialogComponent';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ConfirmDialogComponent } from './ConfirmDialogComponent';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SedeService } from '@app/services/sede.service';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import {
  UserStatusService,
  UserStatus,
} from '@app/services/user-ws/user-status.service';
import { UserWsService } from '@app/services/user-ws/user-ws.service';
import { MatPaginator } from '@angular/material/paginator';

// Importaciones para la paginación de Angular Material
import { RegistrationComponent } from '../registration/registration.component';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
})
export class UserListComponent implements OnInit, OnDestroy {
  // Referencia al paginador de Angular Material
  @ViewChild('paginator') paginator!: MatPaginator;

  // Observables para la paginación y la búsqueda
  usersPage$: Observable<UserPageResponse | null>;
  loadingPage$: Observable<boolean>;
  errorPage$: Observable<string | null>;
  loading$: Observable<boolean | null>;

  // Variables para la paginación de Angular Material
  currentPage = 0; // pageIndex
  pageSize = 10; // pageSize por defecto

  // Variable para la búsqueda
  searchTerm = '';

  // Columnas para la tabla de datos
  tableColumns = [
    { property: 'nombre', name: 'Nombres', type: 'text', sortable: true },
    { property: 'apellido', name: 'Apellidos', type: 'text', sortable: true },
    { property: 'username', name: 'Usuario', type: 'text', sortable: true },
    { property: 'role', name: 'Rol', type: 'text', sortable: true },
    { property: 'sede', name: 'Sede', type: 'text', sortable: true },
    {
      property: 'coordinador',
      name: 'Coordinador',
      type: 'text',
      sortable: false,
    },
    { property: 'estado', name: 'Estado', type: 'text', sortable: true },
    { property: 'online', name: 'Online', type: 'text', sortable: true },
  ];
  tableData: any[] = [];
  tableLoading = false;
  tableTotalItems = 0;

  // Columnas a mostrar en la tabla
  displayedColumns: string[] = [
    'id',
    'nombre',
    'username',
    'sede',
    'role',
    'coordinador',
    'fechaCreacion',
    'estado',
    'online',
    'acciones',
  ];

  // Ya no usamos la variable isDarkTheme, usamos la sintaxis dark: de Tailwind

  // Estado de conexión de los usuarios
  onlineUsers: UserStatus[] = [];

  // Variable para la búsqueda
  searchText = '';

  // Variables para el filtro por sede
  sedes: { id: number; nombre: string }[] = [];
  sedeSeleccionada: number | null = null;
  filterForm: FormGroup;

  // Variable para controlar si hay una búsqueda en curso
  private isSearchInProgress = false;

  // Subject para manejar los cambios en el input de búsqueda con debounce
  private searchSubject: Subject<string> = new Subject<string>();

  // Suscripciones para limpiar al destruir el componente
  private subscriptions: Subscription[] = [];

  constructor(
    private store: Store<State>,
    public dialog: MatDialog,
    private snackBar: MatSnackBar,
    private fb: FormBuilder,
    private sedeService: SedeService,
    public webSocketService: WebSocketService,
    private userStatusService: UserStatusService,
    private userWsService: UserWsService
  ) {
    this.usersPage$ = this.store.select(getPaginatedUsers);
    this.loadingPage$ = this.store.select(getLoadingPage);
    this.errorPage$ = this.store.select(getErrorPage);
    this.loading$ = this.store.pipe(select(getLoading));

    // Inicializar el formulario de filtros
    this.filterForm = this.fb.group({
      sede: [''],
    });
  }

  ngOnInit(): void {
    // Ya no necesitamos detectar el tema oscuro aquí, Tailwind se encarga con la sintaxis dark:

    // Cargar las sedes disponibles
    this.cargarSedes();

    // Inicializar WebSocket si no está conectado
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();
    }

    // Suscribirse a los cambios en el filtro de sede
    const sedeSubscription = this.filterForm
      .get('sede')
      ?.valueChanges.subscribe((sede) => {
        this.sedeSeleccionada = sede;
        this.currentPage = 0;

        // Mostrar inmediatamente el indicador de carga
        this.tableLoading = true;

        // Aplicar filtros y resetear a la primera página
        this.aplicarFiltros(true);
      });

    if (sedeSubscription) {
      this.subscriptions.push(sedeSubscription);
    }

    // Suscribimos al subject con debounceTime de 300ms para búsqueda en tiempo real
    const searchSubscription = this.searchSubject
      .pipe(
        debounceTime(300) // Esperar 300ms después de la última entrada
      )
      .subscribe(() => {
        // Reiniciamos a la primera página cada vez que cambia la búsqueda
        this.currentPage = 0;

        // Aplicar filtros con el nuevo valor de búsqueda y resetear a la primera página
        this.aplicarFiltros(true);
      });

    this.subscriptions.push(searchSubscription);

    // Suscribirse a los cambios en la página de usuarios
    const usersPageSubscription = this.usersPage$.subscribe((page) => {
      if (page) {
        this.tableData = page.users;
        this.tableTotalItems = page.totalItems;
      }
    });

    this.subscriptions.push(usersPageSubscription);

    // Suscribirse a los cambios en el estado de carga
    const loadingSubscription = this.loadingPage$.subscribe((loading) => {
      // Si ya tenemos datos y estamos cargando, mostrar un indicador discreto
      // Si no tenemos datos o es la primera carga, mostrar el indicador completo
      if (loading) {
        // Evitar que el indicador de carga aparezca y desaparezca rápidamente
        // Solo mostrar el indicador si la carga tarda más de 300ms
        setTimeout(() => {
          if (loading) {
            this.tableLoading = true;
          }
        }, 300);
      } else {
        // Cuando termina la carga, ocultar el indicador después de un breve retraso
        // para evitar parpadeos en cargas rápidas
        setTimeout(() => {
          this.tableLoading = false;
        }, 200);
      }
    });

    this.subscriptions.push(loadingSubscription);

    // Suscribirse a los cambios en la lista de usuarios desde WebSocket
    const usersWsSubscription = this.userWsService
      .getUsers()
      .subscribe((users) => {
        if (users && users.length > 0) {
          // Guardar la página actual antes de actualizar los datos
          const currentPageBeforeUpdate = this.currentPage;

          // Actualizar la tabla con los usuarios recibidos por WebSocket
          this.tableData = users;
          this.tableLoading = false;

          // Verificar si hay una búsqueda activa
          const hasActiveSearch =
            this.searchTerm && this.searchTerm.trim() !== '';

          // Si hay búsqueda activa, asegurarse de que la página actual no cambie
          if (hasActiveSearch && this.currentPage !== currentPageBeforeUpdate) {
            console.log(
              `Restaurando página a ${currentPageBeforeUpdate} después de actualización con búsqueda activa`
            );
            this.currentPage = currentPageBeforeUpdate;
            setTimeout(() => {
              this.updatePaginator(currentPageBeforeUpdate);
            }, 0);
          }
        }
      });

    this.subscriptions.push(usersWsSubscription);

    // Suscribirse a la información de paginación
    const paginationSubscription = this.userWsService
      .getPagination()
      .subscribe((pagination) => {
        if (pagination) {
          // Guardar la página actual antes de cualquier actualización
          const currentPageBeforeUpdate = this.currentPage;

          // Actualizar el total de items y páginas
          this.tableTotalItems = pagination.totalItems;

          // Verificar si hay una búsqueda activa
          const hasActiveSearch =
            this.searchTerm && this.searchTerm.trim() !== '';

          // Solo actualizar la página actual si es una solicitud explícita de cambio de página
          // y no una actualización en tiempo real
          const isPageChangeRequest =
            // Si la página actual del componente es diferente a la que viene en la paginación
            // y la página que viene en la paginación no es 0 (valor por defecto)
            this.currentPage !== pagination.currentPage &&
            pagination.currentPage !== 0;

          // Si hay una solicitud explícita de cambio de página y no hay búsqueda activa
          // o si es la primera carga (currentPage es 0)
          if (
            isPageChangeRequest &&
            (!hasActiveSearch || this.currentPage === 0)
          ) {
            console.log(
              `Actualizando página actual de ${this.currentPage} a ${pagination.currentPage}`
            );
            this.currentPage = pagination.currentPage;

            // Actualizar el paginador para reflejar el cambio de página
            setTimeout(() => {
              this.updatePaginator(pagination.currentPage);
            }, 0);
          } else {
            // En cualquier otro caso, mantener la página actual
            // Esto evita que la paginación se reinicie cuando se conectan nuevos usuarios
            // o cuando otros usuarios realizan búsquedas
            if (this.currentPage !== currentPageBeforeUpdate) {
              console.log(
                `Manteniendo página actual ${currentPageBeforeUpdate} (evitando cambio a ${this.currentPage})`
              );
              this.currentPage = currentPageBeforeUpdate;

              // Actualizar el paginador para reflejar la página correcta
              setTimeout(() => {
                this.updatePaginator(currentPageBeforeUpdate);
              }, 0);
            }
          }

          // Solo actualizar el tamaño de página si es diferente para evitar ciclos
          if (
            this.pageSize !== pagination.pageSize &&
            pagination.pageSize !== 10
          ) {
            this.pageSize = pagination.pageSize;
          }
        }
      });

    this.subscriptions.push(paginationSubscription);

    // No necesitamos suscribirnos directamente a los tópicos WebSocket aquí
    // porque ya estamos suscritos a través del servicio UserWsService

    // Solicitar la lista de usuarios a través de WebSocket
    // No preservar la página en la carga inicial
    this.requestUsersViaWebSocket(false);

    // Suscribirse a los eventos WebSocket para usuarios
    this.setupWebSocketSubscriptions();

    // Suscribirse a los cambios en el estado de todos los usuarios
    const userStatusSubscription = this.userStatusService
      .getUsersStatus()
      .subscribe((users) => {
        // Verificar si hay una búsqueda activa
        const hasActiveSearch =
          this.searchTerm && this.searchTerm.trim() !== '';

        // Guardar la página actual antes de cualquier actualización
        const currentPageBeforeUpdate = this.currentPage;

        // Actualizar la lista de usuarios conectados
        this.onlineUsers = users;

        // Forzar la detección de cambios para actualizar la UI
        // Pasamos false para que no recargue los datos, solo actualice la UI
        this.refreshTableData(false);

        // Si hay búsqueda activa, asegurarse de que la página actual no cambie
        if (hasActiveSearch && this.currentPage !== currentPageBeforeUpdate) {
          console.log(
            `Restaurando página a ${currentPageBeforeUpdate} después de actualización con búsqueda activa`
          );
          this.currentPage = currentPageBeforeUpdate;
          setTimeout(() => {
            this.updatePaginator(currentPageBeforeUpdate);
          }, 0);
        }

        console.log(
          `Actualización de estado de usuario recibida. Manteniendo página actual: ${this.currentPage}`
        );
      });

    this.subscriptions.push(userStatusSubscription);

    // Suscribirse a los eventos de conexión/desconexión del WebSocket
    const wsConnectionSubscription = this.webSocketService
      .getConnectionStatus()
      .subscribe((connected) => {
        if (!connected) {
          console.log(
            'UserListComponent: WebSocket desconectado, marcando usuarios como offline'
          );
          // Marcar todos los usuarios como desconectados cuando se desconecta el WebSocket
          if (this.onlineUsers.length > 0) {
            this.onlineUsers = this.onlineUsers.map((user) => ({
              ...user,
              online: false,
              status: 'OFFLINE',
            }));
            // Forzar la detección de cambios para actualizar la UI
            this.refreshTableData(false);
          }
        } else {
          // Si se reconecta, actualizar la lista de usuarios solo si no hay búsqueda activa
          console.log(
            'UserListComponent: WebSocket conectado, actualizando lista de usuarios'
          );

          // Verificar si hay una búsqueda activa
          const hasActiveSearch =
            this.searchTerm && this.searchTerm.trim() !== '';

          if (!hasActiveSearch) {
            // Si no hay búsqueda activa, recargar la lista de usuarios
            // Preservar la página actual al reconectar
            setTimeout(() => {
              this.requestUsersViaWebSocket(true);
            }, 1000);
          } else {
            // Si hay búsqueda activa, solo actualizar la UI sin recargar datos
            console.log(
              'Hay búsqueda activa, no se recarga la lista de usuarios'
            );
            this.refreshTableData(false);
          }
        }
      });
    this.subscriptions.push(wsConnectionSubscription);
  }

  /**
   * Configura las suscripciones a los eventos WebSocket para usuarios
   */
  private setupWebSocketSubscriptions(): void {
    // Suscribirse a la creación de usuarios
    const createSubscription = this.webSocketService
      .getMessagesByType('USER_CREATED')
      .subscribe((message: any) => {
        console.log('Usuario creado recibido por WebSocket:', message);

        // Mostrar notificación
        this.snackBar.open(
          `Nuevo usuario registrado: ${message.user?.nombre || 'Usuario'} ${
            message.user?.apellido || ''
          }`,
          'Cerrar',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['info-snackbar'],
          }
        );

        // Actualizar la tabla con el nuevo usuario
        if (message.user) {
          // Verificar si el usuario ya existe en la tabla
          const existingIndex = this.tableData.findIndex(
            (u) => u.id === message.user.id
          );
          if (existingIndex === -1) {
            // Si no existe, añadirlo al principio de la tabla
            this.tableData = [message.user, ...this.tableData];
            // Actualizar el contador total
            this.tableTotalItems++;
          }
        }
      });

    this.subscriptions.push(createSubscription);

    // Suscribirse a la actualización de usuarios
    const updateSubscription = this.webSocketService
      .getMessagesByType('USER_UPDATED')
      .subscribe((message: any) => {
        console.log('Usuario actualizado recibido por WebSocket:', message);

        // Mostrar notificación
        this.snackBar.open(
          `Usuario actualizado: ${message.user?.nombre || 'Usuario'} ${
            message.user?.apellido || ''
          }`,
          'Cerrar',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['info-snackbar'],
          }
        );

        // Actualizar el usuario en la tabla si está presente
        if (message.user && this.tableData) {
          const index = this.tableData.findIndex(
            (u) => u.id === message.user.id
          );
          if (index !== -1) {
            // Crear una copia de la tabla para forzar la detección de cambios
            const updatedTableData = [...this.tableData];
            updatedTableData[index] = message.user;
            this.tableData = updatedTableData;
          } else {
            // Si el usuario no está en la tabla actual pero debería estarlo según los filtros,
            // recargar la tabla completa
            this.onTableRefresh(true);
          }
        }
      });

    this.subscriptions.push(updateSubscription);

    // Suscribirse a la eliminación de usuarios
    const deleteSubscription = this.webSocketService
      .getMessagesByType('USER_DELETED')
      .subscribe((message: any) => {
        console.log('Usuario eliminado recibido por WebSocket:', message);

        // Mostrar notificación
        this.snackBar.open(
          `Usuario eliminado: ${message.user?.nombre || 'Usuario'} ${
            message.user?.apellido || ''
          }`,
          'Cerrar',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
            panelClass: ['warning-snackbar'],
          }
        );

        // Eliminar el usuario de la tabla si está presente
        if (message.user && this.tableData) {
          // Verificar si el usuario está en la tabla actual
          const index = this.tableData.findIndex(
            (u) => u.id === message.user.id
          );
          if (index !== -1) {
            // Crear una copia de la tabla para forzar la detección de cambios
            const updatedTableData = [...this.tableData];
            // Actualizar el estado del usuario a inactivo en lugar de eliminarlo
            if (message.user.estado === 'I') {
              updatedTableData[index] = {
                ...updatedTableData[index],
                estado: 'I',
              };
              this.tableData = updatedTableData;
            } else {
              // Si es una eliminación definitiva, quitar de la tabla
              this.tableData = this.tableData.filter(
                (u) => u.id !== message.user.id
              );
              // Actualizar el contador total
              this.tableTotalItems--;
            }
          }
        }
      });

    this.subscriptions.push(deleteSubscription);

    // Suscribirse a la desconexión de usuarios
    const disconnectSubscription = this.webSocketService
      .getMessagesByType('USER_DISCONNECTED')
      .subscribe((message: any) => {
        console.log('Usuario desconectado recibido por WebSocket:', message);

        // Verificar si hay una búsqueda activa
        const hasActiveSearch =
          this.searchTerm && this.searchTerm.trim() !== '';

        // Guardar la página actual antes de cualquier actualización
        const currentPageBeforeUpdate = this.currentPage;

        // Actualizar el estado del usuario en la lista de usuarios conectados
        if (message.userId && this.onlineUsers.length > 0) {
          // Buscar el usuario en la lista de usuarios conectados
          const userId = Number(message.userId);
          const userIndex = this.onlineUsers.findIndex(
            (u) => Number(u.userId) === userId
          );

          if (userIndex !== -1) {
            // Crear una copia de la lista para forzar la detección de cambios
            const updatedOnlineUsers = [...this.onlineUsers];
            updatedOnlineUsers[userIndex] = {
              ...updatedOnlineUsers[userIndex],
              online: false,
              status: 'OFFLINE',
            };
            this.onlineUsers = updatedOnlineUsers;

            console.log(
              `UserListComponent: Usuario ${userId} marcado como desconectado`
            );

            // Forzar la actualización de la tabla sin recargar datos
            this.refreshTableData(false);

            // Si hay búsqueda activa, asegurarse de que la página actual no cambie
            if (
              hasActiveSearch &&
              this.currentPage !== currentPageBeforeUpdate
            ) {
              console.log(
                `Restaurando página a ${currentPageBeforeUpdate} después de desconexión con búsqueda activa`
              );
              this.currentPage = currentPageBeforeUpdate;
              setTimeout(() => {
                this.updatePaginator(currentPageBeforeUpdate);
              }, 0);
            }
          }
        }
      });

    this.subscriptions.push(disconnectSubscription);

    // Suscribirse a los eventos de conexión/desconexión generales
    const connectionClosedSubscription = this.webSocketService
      .getMessagesByType('CONNECTION_CLOSED')
      .subscribe(() => {
        console.log('UserListComponent: Conexión WebSocket cerrada');

        // Solicitar una actualización de la lista de usuarios
        // Preservar la página actual al reconectar
        setTimeout(() => {
          this.onTableRefresh(true);
        }, 1000);
      });

    this.subscriptions.push(connectionClosedSubscription);

    // Suscribirse a los mensajes generales del WebSocket para usuarios
    const usersTopicSubscription = this.webSocketService
      .getMessagesByDestination('/topic/users')
      .subscribe((message: any) => {
        console.log('Mensaje recibido en /topic/users:', message);

        // Verificar si hay una búsqueda activa
        const hasActiveSearch =
          this.searchTerm && this.searchTerm.trim() !== '';

        // Procesar según el tipo de operación
        if (message && message.operation) {
          switch (message.operation) {
            case 'CREATED':
              this.handleUserCreated(message.user);
              // Forzar actualización de la tabla solo si no hay búsqueda activa
              if (!hasActiveSearch) {
                this.refreshTableData(true);
              } else {
                // Si hay búsqueda activa, solo actualizar la UI sin recargar datos
                this.refreshTableData(false);
              }
              break;
            case 'UPDATED':
              this.handleUserUpdated(message.user);
              break;
            case 'DELETED':
              this.handleUserDeleted(message.user);
              break;
          }
        }
      });

    this.subscriptions.push(usersTopicSubscription);
  }

  // Lógica para cargar usuarios desde NGRX
  loadUsers(_page: number, _size: number): void {
    // Aplicar filtros actuales
    this.aplicarFiltros();
  }

  // Cargar las sedes disponibles
  cargarSedes(): void {
    // Obtener todas las sedes desde el servicio
    const sedesSubscription = this.sedeService
      .getAllSedes()
      .subscribe((response) => {
        if (response.rpta === 1 && response.data) {
          // Guardar las sedes con su ID y nombre
          this.sedes = response.data.map((sede) => ({
            id: sede.id || 0,
            nombre: sede.nombre,
          }));
          // Ordenar alfabéticamente por nombre
          this.sedes.sort((a, b) => a.nombre.localeCompare(b.nombre));
        }
      });

    this.subscriptions.push(sedesSubscription);
  }

  // Aplicar filtros (búsqueda y sede)
  aplicarFiltros(resetPage: boolean = false): void {
    // Si ya hay una búsqueda en curso, no iniciar otra
    if (this.isSearchInProgress) {
      return;
    }

    // Marcar que hay una búsqueda en curso
    this.isSearchInProgress = true;

    // Mostrar el indicador de carga
    this.tableLoading = true;

    // Si se solicita resetear la página, volver a la página 0
    if (resetPage) {
      this.currentPage = 0;
      // Actualizar el paginador para reflejar el cambio de página
      setTimeout(() => {
        this.updatePaginator(0);
      }, 0);
    }

    // Mantener el caso original para permitir búsquedas por nombre completo con mayúsculas/minúsculas correctas
    // Asegurarse de que searchTerm y searchText estén sincronizados
    if (this.searchText !== this.searchTerm) {
      this.searchTerm = this.searchText;
    }

    const searchValue = this.searchTerm.trim();
    const sedeId = this.sedeSeleccionada;

    console.log(
      `Aplicando filtros - Búsqueda: "${searchValue}", Sede: ${
        sedeId || 'todas'
      }, Página: ${this.currentPage}`
    );

    // Guardar la página actual para mantenerla durante la búsqueda
    const pageToRequest = this.currentPage;

    // Si el WebSocket está conectado, usar WebSocket para todas las operaciones
    if (this.webSocketService.isConnected()) {
      if (!searchValue) {
        // Si no hay búsqueda de texto, usar el método normal de WebSocket
        this.userWsService.requestUsers(
          pageToRequest,
          this.pageSize,
          sedeId || undefined
        );
      } else {
        // Si hay búsqueda de texto, usar el nuevo método de búsqueda por WebSocket
        this.userWsService.searchUsers(
          searchValue,
          pageToRequest,
          this.pageSize,
          sedeId || undefined
        );
      }
      this.isSearchInProgress = false;
      return;
    }

    // Si el WebSocket no está disponible, usar HTTP
    if (!searchValue && !sedeId) {
      // Sin filtros pero usando HTTP (WebSocket no disponible)
      this.store.dispatch(
        new fromActions.LoadUsersPage(pageToRequest, this.pageSize)
      );
    } else {
      // Con filtros, usar endpoint de búsqueda
      let query = searchValue;
      this.store.dispatch(
        new fromActions.SearchUsers(query, pageToRequest, this.pageSize, sedeId)
      );
    }

    // Suscribirse a los cambios en el estado de carga para saber cuándo termina la búsqueda
    const loadingSubscription = this.loadingPage$.subscribe((loading) => {
      if (!loading && this.isSearchInProgress) {
        // La búsqueda ha terminado
        this.isSearchInProgress = false;
        // Desuscribirse para evitar múltiples suscripciones
        loadingSubscription.unsubscribe();
      }
    });

    // Añadir la suscripción a la lista para limpiarla al destruir el componente
    this.subscriptions.push(loadingSubscription);
  }

  // ============================================
  //   Manejo de la paginación de Angular Material
  // ============================================
  onPageChange(event: any): void {
    // event.pageIndex => página actual
    // event.pageSize  => tamaño de página
    const newPage = event.pageIndex || 0;
    const newSize = event.pageSize || 10;

    // Verificar si realmente cambió la página o el tamaño
    const pageChanged = this.currentPage !== newPage;
    const sizeChanged = this.pageSize !== newSize;

    if (pageChanged || sizeChanged) {
      // Mostrar indicador de carga
      this.tableLoading = true;

      // Si cambió el tamaño de página, volver a la primera página
      if (sizeChanged) {
        console.log(
          `Cambiando tamaño de página de ${this.pageSize} a ${newSize}, reiniciando a página 0`
        );
        this.currentPage = 0;
        this.pageSize = newSize;

        // Actualizar el paginador para reflejar el cambio a la primera página
        setTimeout(() => {
          if (this.paginator) {
            this.paginator.pageIndex = 0;
          }
        }, 0);
      } else {
        // Solo cambió la página, actualizar valores internos
        this.currentPage = newPage;
        console.log(
          `Cambiando a página ${this.currentPage}, tamaño ${this.pageSize}`
        );
      }

      // Si el WebSocket está conectado, usar WebSocket para todas las operaciones
      if (this.webSocketService.isConnected()) {
        // Mantener el caso original para permitir búsquedas por nombre completo con mayúsculas/minúsculas correctas
        const searchValue = this.searchTerm.trim();

        if (!searchValue) {
          // Si no hay búsqueda de texto, usar el método normal de WebSocket
          this.userWsService.requestUsers(
            this.currentPage,
            this.pageSize,
            this.sedeSeleccionada || undefined
          );
        } else {
          // Si hay búsqueda de texto, usar el nuevo método de búsqueda por WebSocket
          this.userWsService.searchUsers(
            searchValue,
            this.currentPage,
            this.pageSize,
            this.sedeSeleccionada || undefined
          );
        }
      } else {
        // Si el WebSocket no está disponible, usar HTTP
        // Mantener la página actual
        this.aplicarFiltros(false);
      }

      // Asegurarse de que el paginador refleje el cambio de página
      if (this.paginator && this.paginator.pageIndex !== this.currentPage) {
        this.paginator.pageIndex = this.currentPage;
      }
    }
  }

  // Métodos para la tabla de datos
  onTableAdd(): void {
    this.openCreateUserDialog();
  }

  onTableEdit(user: any): void {
    this.openEditUserDialog(user);
  }

  onTableDelete(user: any): void {
    this.eliminarUsuario(user);
  }

  onTableRefresh(preservePage: boolean = true): void {
    // Si ya hay datos y no es una acción explícita del usuario (como hacer clic en el botón de refrescar),
    // evitar mostrar el indicador de carga completo
    const isUserInitiatedRefresh = true; // Asumimos que es iniciado por el usuario
    const hasExistingData = this.tableData && this.tableData.length > 0;

    // Solo mostrar el indicador de carga completo si no hay datos o es una acción explícita del usuario
    if (!hasExistingData || isUserInitiatedRefresh) {
      // Usar un temporizador para evitar parpadeos en cargas rápidas
      setTimeout(() => {
        this.tableLoading = true;
      }, 100);
    }

    console.log(
      `Refrescando tabla de usuarios (preservar página: ${preservePage})`
    );

    // Si el WebSocket está conectado, usar WebSocket para todas las operaciones
    if (this.webSocketService.isConnected()) {
      // Mantener el caso original para permitir búsquedas por nombre completo con mayúsculas/minúsculas correctas
      const searchValue = this.searchTerm.trim();

      if (!searchValue) {
        console.log('No hay búsqueda, usando WebSocket normal');
        // Si no hay búsqueda de texto, usar el método normal de WebSocket
        this.requestUsersViaWebSocket(preservePage);
      } else {
        console.log('Hay búsqueda, usando WebSocket de búsqueda');
        // Si hay búsqueda de texto, usar el nuevo método de búsqueda por WebSocket

        // Determinar la página a solicitar
        const pageToRequest = preservePage ? this.currentPage : 0;

        // Si no estamos preservando la página, reiniciar a la página 0
        if (!preservePage) {
          this.currentPage = 0;
          // Actualizar el paginador para reflejar el cambio de página
          setTimeout(() => {
            this.updatePaginator(0);
          }, 0);
        }

        this.userWsService.searchUsers(
          searchValue,
          pageToRequest,
          this.pageSize,
          this.sedeSeleccionada || undefined
        );
      }
    } else {
      console.log('WebSocket no disponible, usando HTTP');
      // Si el WebSocket no está disponible, usar HTTP

      // Si no estamos preservando la página, reiniciar a la página 0
      if (!preservePage) {
        this.currentPage = 0;
      }

      this.aplicarFiltros();
    }
  }

  /**
   * Solicita la lista de usuarios a través de WebSocket
   * @param preservePage Si es true, mantiene la página actual en lugar de reiniciar a la página 0
   */
  requestUsersViaWebSocket(preservePage: boolean = false): void {
    // Determinar si ya tenemos datos para decidir cómo mostrar el indicador de carga
    const hasExistingData = this.tableData && this.tableData.length > 0;

    // Si ya hay datos, mostrar un indicador de carga discreto con retraso para evitar parpadeos
    if (hasExistingData) {
      setTimeout(() => {
        // Solo mostrar el indicador si la carga aún está en progreso
        if (!this.tableData || this.tableData.length === 0) {
          this.tableLoading = true;
        }
      }, 300);
    } else {
      // Si no hay datos, mostrar el indicador de carga completo
      this.tableLoading = true;
    }

    // Determinar la página a solicitar
    const pageToRequest = preservePage ? this.currentPage : 0;

    // Si no estamos preservando la página, reiniciar a la página 0
    if (!preservePage) {
      this.currentPage = 0;
      // Actualizar el paginador para reflejar el cambio de página
      setTimeout(() => {
        this.updatePaginator(0);
      }, 0);
    }

    // Registrar la solicitud para depuración
    console.log(
      `Solicitando usuarios vía WebSocket - Página: ${pageToRequest}, Tamaño: ${this.pageSize}, Preservar página: ${preservePage}`
    );

    // Verificar si el WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      // Asegurarse de que el servicio UserWsService esté inicializado
      if (!this.userWsService['initialized']) {
        this.userWsService.initialize();

        // Esperar un momento para que se complete la inicialización
        setTimeout(() => {
          // Ahora pasamos también el sedeId si existe
          this.userWsService.requestUsers(
            pageToRequest,
            this.pageSize,
            this.sedeSeleccionada || undefined
          );
        }, 500);
      } else {
        // Enviar mensaje al servidor para solicitar la lista de usuarios
        // Ahora pasamos también el sedeId si existe
        this.userWsService.requestUsers(
          pageToRequest,
          this.pageSize,
          this.sedeSeleccionada || undefined
        );
      }
    } else {
      // Intentar conectar el WebSocket
      this.webSocketService.connect();

      // Esperar un momento para ver si se conecta
      setTimeout(() => {
        if (this.webSocketService.isConnected()) {
          this.requestUsersViaWebSocket(preservePage);
        } else {
          // Si no se puede conectar, usar HTTP pero mantener la página si es necesario
          if (preservePage) {
            // Mantener la página actual al aplicar filtros
            this.aplicarFiltros(false);
          } else {
            // Reiniciar a la página 0
            this.currentPage = 0;
            this.aplicarFiltros(true);
          }
        }
      }, 1000);
    }
  }

  // Método específico para manejar el cambio de tamaño de página
  onPageSizeChange(newSize: string): void {
    console.log(`onPageSizeChange: Cambiando tamaño de página a ${newSize}`);

    // Convertir el valor a número
    const numericSize = parseInt(newSize, 10);

    if (isNaN(numericSize) || numericSize <= 0) {
      console.error(`Tamaño de página inválido: ${newSize}`);
      return;
    }

    // Verificar si realmente cambió el tamaño
    if (this.pageSize !== numericSize) {
      // Mostrar indicador de carga
      this.tableLoading = true;

      // Actualizar el tamaño de página y reiniciar a la primera página
      this.pageSize = numericSize;
      this.currentPage = 0;

      console.log(
        `Tamaño de página actualizado a ${this.pageSize}, reiniciando a página 0`
      );

      // Solicitar datos con el nuevo tamaño de página
      if (this.webSocketService.isConnected()) {
        // Mantener el caso original para permitir búsquedas por nombre completo con mayúsculas/minúsculas correctas
        const searchValue = this.searchTerm.trim();

        if (!searchValue) {
          // Si no hay búsqueda de texto, usar el método normal de WebSocket
          this.userWsService.requestUsers(
            0,
            this.pageSize,
            this.sedeSeleccionada || undefined
          );
        } else {
          // Si hay búsqueda de texto, usar el método de búsqueda por WebSocket
          this.userWsService.searchUsers(
            searchValue,
            0,
            this.pageSize,
            this.sedeSeleccionada || undefined
          );
        }
      } else {
        // Si el WebSocket no está disponible, usar HTTP
        this.aplicarFiltros(true);
      }

      // Actualizar el paginador para reflejar los cambios
      setTimeout(() => {
        this.updatePaginator(0);
      }, 0);
    }
  }

  // Si usas un mat-select externo para "Mostrar X registros"
  // que no sea el del MatPaginator, puedes forzar un reload:
  onPageChangeViaSelect(): void {
    // Resetea la página a 0 si cambias el "Mostrar X registros"
    this.currentPage = 0;
    // Resetear a la primera página
    this.aplicarFiltros(true);
  }

  // ============================================
  //                 BÚSQUEDA
  // ============================================
  // Cuando cambia el input de búsqueda
  onSearchChange(value: string): void {
    // Actualizar searchTerm con el valor actual, sin convertir a minúsculas
    // para permitir búsquedas por nombre completo con mayúsculas/minúsculas correctas
    this.searchTerm = value;

    // Emitir el evento de búsqueda
    this.searchSubject.next(value);

    // Registrar la búsqueda para depuración
    console.log('Búsqueda por nombre completo:', value);

    // Si el valor está vacío, limpiar la búsqueda
    if (!value || value.trim() === '') {
      this.clearSearch();
    }
  }

  // ============================================
  //        CRUD USUARIOS (Eliminar, Crear, Editar)
  // ============================================
  eliminarUsuario(user: User): void {
    // Determinar el tipo de eliminación según el estado actual del usuario
    const isInactive = user.estado === 'I';
    const title = isInactive
      ? 'Confirmar eliminación definitiva'
      : 'Confirmar desactivación';
    const message = isInactive
      ? `¿Está seguro de eliminar definitivamente al usuario ${user.nombre}?`
      : `¿Está seguro de desactivar al usuario ${user.nombre}?`;
    const confirmText = isInactive ? 'Eliminar definitivamente' : 'Desactivar';

    const confirmDialog = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: 'Cancelar',
      },
    });

    const dialogSubscription = confirmDialog
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          // Dispatch la acción para eliminar el usuario
          this.store.dispatch(new fromActions.DeleteUser(user.id));

          // Mostrar notificación local según el tipo de operación
          const notificationMessage = isInactive
            ? 'Procesando eliminación definitiva...'
            : 'Procesando desactivación...';

          this.snackBar.open(notificationMessage, 'Cerrar', {
            duration: 2000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });

          // Actualizar inmediatamente la UI para mejor experiencia de usuario
          if (!isInactive) {
            // Si estamos desactivando, actualizar el estado en la tabla inmediatamente
            const index = this.tableData.findIndex((u) => u.id === user.id);
            if (index !== -1) {
              const updatedTableData = [...this.tableData];
              updatedTableData[index] = {
                ...updatedTableData[index],
                estado: 'I',
              };
              this.tableData = updatedTableData;
            }
          } else {
            // Si es eliminación definitiva, quitar de la tabla inmediatamente
            this.tableData = this.tableData.filter((u) => u.id !== user.id);
            this.tableTotalItems--;
          }
        }
      });

    this.subscriptions.push(dialogSubscription);
  }

  openCreateUserDialog(): void {
    // Detectar el ancho de la pantalla para ajustar el tamaño del modal
    const screenWidth = window.innerWidth;
    let dialogWidth = '90vw';
    let dialogMaxWidth = '800px'; // Ancho máximo para pantallas grandes

    // En pantallas pequeñas, usar un ancho más reducido
    if (screenWidth < 768) {
      dialogWidth = '95vw';
      dialogMaxWidth = '600px';
    }

    const dialogRef = this.dialog.open(RegistrationIndividualComponent, {
      width: dialogWidth,
      maxWidth: dialogMaxWidth,
      disableClose: false,
      // Configuración para asegurar que el modal se ajuste a la pantalla
      maxHeight: '90vh',
      height: 'auto',
      panelClass: ['user-registration-dialog'],
    });

    const dialogSubscription = dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Mostrar notificación local (la notificación WebSocket se mostrará cuando llegue el evento)
        this.snackBar.open('Procesando creación de usuario...', 'Cerrar', {
          duration: 2000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });

        // No es necesario recargar la lista manualmente, ya que el WebSocket notificará a todos los clientes
        // y actualizará la interfaz automáticamente cuando se complete la creación
      }
    });

    this.subscriptions.push(dialogSubscription);
  }

  openCreateUserDialogMasivo(): void {
    const dialogRef = this.dialog.open(RegistrationComponent, {
      width: '600px',
      disableClose: false,
      // Máximo 80% del alto de la pantalla, con scroll automático si excede
      maxHeight: '80vh',
    });

    const dialogSubscription = dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Mostrar notificación local (la notificación WebSocket se mostrará cuando llegue el evento)
        this.snackBar.open('Procesando carga masiva de usuarios...', 'Cerrar', {
          duration: 2000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });

        // No es necesario recargar la lista manualmente, ya que el WebSocket notificará a todos los clientes
        // y actualizará la interfaz automáticamente cuando se complete la creación
      }
    });

    this.subscriptions.push(dialogSubscription);
  }

  openEditUserDialog(user: User): void {
    // Detectar el ancho de la pantalla para ajustar el tamaño del modal
    const screenWidth = window.innerWidth;
    let dialogWidth = '90vw';
    let dialogMaxWidth = '800px'; // Reducido de 1800px a 800px para pantallas grandes

    // En pantallas pequeñas, usar un ancho más reducido
    if (screenWidth < 768) {
      dialogWidth = '95vw';
      dialogMaxWidth = '600px';
    }

    const dialogRef = this.dialog.open(EditUserDialogComponent, {
      width: dialogWidth,
      maxWidth: dialogMaxWidth,
      disableClose: true,
      data: user,
      panelClass: ['modern-modal', 'responsive-dialog'],
      autoFocus: false,
    });

    const dialogSubscription = dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Dispatch la acción para actualizar el usuario (incluido el rol si cambió)
        this.store.dispatch(new fromActions.UpdateUser(result.id, result));

        // Mostrar notificación local (la notificación WebSocket se mostrará cuando llegue el evento)
        this.snackBar.open('Procesando actualización...', 'Cerrar', {
          duration: 2000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });

        // No es necesario recargar la lista manualmente, ya que el WebSocket notificará a todos los clientes
        // y actualizará la interfaz automáticamente cuando se complete la actualización
      }
    });

    this.subscriptions.push(dialogSubscription);
  }

  // ============================================
  //                EXPORTACIONES
  // ============================================
  exportToPNG(): void {
    this.snackBar.open('Exportando a PNG...', 'Cerrar', {
      duration: 2000,
    });
    // Tu lógica real de export...
  }

  exportToExcel(): void {
    this.snackBar.open('Exportando a Excel...', 'Cerrar', {
      duration: 2000,
    });
    // Tu lógica real de export...
  }

  // Métodos para obtener apellido paterno y materno del campo apellido
  getApellidoPaterno(apellido: string): string {
    if (!apellido) return '-';
    const apellidos = apellido.trim().split(' ');
    return apellidos[0] || '-';
  }

  /**
   * Verifica si un usuario está conectado
   * @param userId ID del usuario a verificar
   */
  isUserOnline(userId: number): boolean {
    // Verificar si el usuario está en la lista de usuarios conectados
    const isOnline = this.onlineUsers.some(
      (user) => Number(user.userId) === Number(userId) && user.online
    );

    // Si el usuario no está en la lista, asumir que está desconectado
    if (
      this.onlineUsers.length > 0 &&
      !this.onlineUsers.some((user) => Number(user.userId) === Number(userId))
    ) {
      return false;
    }

    return isOnline;
  }

  /**
   * Obtiene el estado de conexión de un usuario para mostrar en la UI
   * @param userId ID del usuario
   * @param estado Estado del usuario (A=Activo, I=Inactivo)
   */
  getUserConnectionStatus(
    userId: number,
    estado: string
  ): { text: string; color: string } {
    // Si el usuario está inactivo, mostrar como "Inactivo" independientemente de su conexión
    if (estado === 'I') {
      return { text: 'Inactivo', color: '#F44336' };
    }

    // Verificar si el usuario está conectado
    const isOnline = this.isUserOnline(userId);

    return isOnline
      ? { text: 'Conectado', color: '#4CAF50' }
      : { text: 'Desconectado', color: '#F44336' };
  }

  /**
   * Formatea una fecha que viene como array desde el backend
   * @param dateArray Array de fecha [año, mes, día, hora, minuto, segundo]
   * @returns Fecha formateada como string (dd/MM/yyyy)
   */
  formatDateArray(dateArray: any): string {
    if (!dateArray) return '-';

    // Verificar si es un array
    if (Array.isArray(dateArray)) {
      try {
        // El formato del backend es [año, mes, día, hora, minuto, segundo]
        // Nota: en JavaScript los meses van de 0-11, pero el backend envía 1-12
        const year = dateArray[0];
        const month = dateArray[1]; // El mes viene como 1-12
        const day = dateArray[2];

        // Formatear como dd/MM/yyyy
        return `${day.toString().padStart(2, '0')}/${month
          .toString()
          .padStart(2, '0')}/${year}`;
      } catch (error) {
        console.error('Error al formatear fecha:', error);
        return '-';
      }
    }

    // Si no es un array, intentar usar el pipe de fecha
    try {
      return new Date(dateArray).toLocaleDateString();
    } catch (error) {
      console.error('Error al convertir fecha:', error);
      return '-';
    }
  }

  getApellidoMaterno(apellido: string): string {
    if (!apellido) return '-';
    const apellidos = apellido.trim().split(' ');
    return apellidos.length > 1 ? apellidos.slice(1).join(' ') : '-';
  }

  // Método para ver detalles de un usuario
  verDetalles(user: User): void {
    // Puedes implementar este método según tu necesidad
    // Por ejemplo, abrir un diálogo con los detalles del usuario
    this.snackBar.open(
      `Detalles del usuario: ${user.nombre} ${user.apellido}`,
      'Cerrar',
      {
        duration: 3000,
      }
    );
  }

  /**
   * Actualiza el paginador de Angular Material
   * @param pageIndex Índice de página (0-based)
   */
  updatePaginator(pageIndex: number = 0): void {
    // Asegurarse de que el paginador esté disponible
    if (this.paginator) {
      console.log(
        `Actualizando paginador a página ${pageIndex}, tamaño ${this.pageSize}`
      );

      // Actualizar el pageIndex del paginador
      this.paginator.pageIndex = pageIndex;

      // Asegurarse de que el tamaño de página en el paginador coincida con el del componente
      if (this.paginator.pageSize !== this.pageSize) {
        console.log(
          `Actualizando tamaño de página en el paginador de ${this.paginator.pageSize} a ${this.pageSize}`
        );
        this.paginator.pageSize = this.pageSize;
      }

      // Actualizar la información de paginación en el servicio WebSocket
      if (this.webSocketService.isConnected() && this.userWsService) {
        // Forzar una actualización de la información de paginación en el servicio
        this.userWsService['paginationSubject'].next({
          totalItems: this.tableTotalItems,
          totalPages: Math.ceil(this.tableTotalItems / this.pageSize),
          currentPage: pageIndex,
          pageSize: this.pageSize,
        });
      }

      // Forzar la detección de cambios en el paginador
      setTimeout(() => {
        if (this.paginator) {
          // Actualizar la vista del paginador
          this.paginator.page.emit({
            pageIndex: pageIndex,
            pageSize: this.pageSize,
            length: this.tableTotalItems,
            previousPageIndex: this.currentPage,
          });
        }
      }, 0);
    } else {
      console.warn('Paginador no disponible para actualizar');
    }
  }

  resetSearch(): void {
    this.searchTerm = '';
    this.currentPage = 0;

    // Mostrar indicador de carga
    this.tableLoading = true;

    // Aplicar filtros y asegurar que la paginación se actualice
    // Resetear a la primera página
    this.aplicarFiltros(true);

    // Actualizar el paginador
    this.updatePaginator(0);
  }

  clearSearch(): void {
    // Limpiar los campos de búsqueda
    this.searchText = '';
    this.searchTerm = '';
    this.currentPage = 0;

    // Mostrar indicador de carga
    this.tableLoading = true;

    // Emitir un evento vacío para asegurar que se resetee la búsqueda
    this.searchSubject.next('');

    console.log('Búsqueda limpiada, cargando todos los usuarios');

    // Solicitar la lista completa de usuarios sin filtros
    if (this.webSocketService.isConnected()) {
      this.userWsService.requestUsers(
        0,
        this.pageSize,
        this.sedeSeleccionada || undefined
      );
    } else {
      // Si el WebSocket no está disponible, usar HTTP
      this.store.dispatch(new fromActions.LoadUsersPage(0, this.pageSize));
    }

    // Actualizar el paginador
    this.updatePaginator(0);
  }

  // Limpiar todos los filtros
  clearAllFilters(): void {
    console.log('Limpiando todos los filtros');

    // Limpiar búsqueda
    this.searchText = '';
    this.searchTerm = '';

    // Limpiar filtro de sede
    this.filterForm.get('sede')?.setValue(null);
    this.sedeSeleccionada = null;

    // Resetear página
    this.currentPage = 0;

    // Mostrar indicador de carga
    this.tableLoading = true;

    // Usar WebSocket para obtener la lista completa
    console.log(
      'Solicitando lista de usuarios por WebSocket después de limpiar filtros'
    );
    // No preservar la página al limpiar filtros, volver a la página 1
    this.requestUsersViaWebSocket(false);

    // Actualizar el paginador
    this.updatePaginator(0);
  }

  /**
   * Actualiza la tabla para reflejar los cambios en el estado de los usuarios
   * @param forceReload Si es true, recarga los datos desde el servidor
   */
  private refreshTableData(forceReload: boolean = false): void {
    // Si hay una búsqueda activa, solo actualizar la UI sin recargar datos
    const hasActiveSearch = this.searchTerm && this.searchTerm.trim() !== '';

    // Guardar la página actual antes de cualquier actualización
    const currentPageBeforeRefresh = this.currentPage;

    if (forceReload && !hasActiveSearch) {
      // Recargar datos desde el servidor solo si no hay búsqueda activa
      if (this.webSocketService.isConnected() && !this.sedeSeleccionada) {
        // Si no hay filtros de sede y el WebSocket está conectado, usar WebSocket
        // Pasar la página actual para mantener la paginación
        this.requestUsersViaWebSocket(true);
      } else {
        // Si hay filtros de sede o el WebSocket no está disponible, usar HTTP
        // pero mantener la página actual
        this.currentPage = currentPageBeforeRefresh;
        this.onTableRefresh(true);
      }

      // Asegurarse de que la página actual se mantenga después de la actualización
      setTimeout(() => {
        if (this.currentPage !== currentPageBeforeRefresh) {
          console.log(
            `Restaurando página a ${currentPageBeforeRefresh} después de forzar recarga`
          );
          this.currentPage = currentPageBeforeRefresh;
          this.updatePaginator(currentPageBeforeRefresh);
        }
      }, 500);

      return;
    }

    if (this.tableData && this.tableData.length > 0) {
      // Crear una copia de la tabla para forzar la detección de cambios
      // sin recargar datos del servidor
      this.tableData = [...this.tableData];

      // Actualizar el estado de conexión de los usuarios en la tabla actual
      if (this.onlineUsers && this.onlineUsers.length > 0) {
        this.tableData = this.tableData.map((user) => {
          const userId = Number(user.id);
          const onlineUser = this.onlineUsers.find(
            (u) => Number(u.userId) === userId
          );

          // Si encontramos el usuario en la lista de usuarios conectados, actualizar su estado
          if (onlineUser) {
            return {
              ...user,
              // Solo actualizamos el estado de conexión, no otras propiedades
              online: onlineUser.online,
            };
          }

          // Si no lo encontramos, devolver el usuario sin cambios
          return { ...user };
        });
      }

      // Asegurarse de que la página actual se mantenga después de la actualización
      if (this.currentPage !== currentPageBeforeRefresh) {
        console.log(
          `Restaurando página a ${currentPageBeforeRefresh} después de actualización de estado`
        );
        this.currentPage = currentPageBeforeRefresh;

        // Actualizar el paginador para reflejar la página correcta
        setTimeout(() => {
          this.updatePaginator(currentPageBeforeRefresh);
        }, 0);
      }
    }
  }

  /**
   * Maneja la creación de un usuario recibido por WebSocket
   * @param user Usuario creado
   */
  private handleUserCreated(user: any): void {
    if (!user) return;

    console.log('Manejando usuario creado:', user);

    // Mostrar notificación
    this.snackBar.open(
      `Nuevo usuario registrado: ${user.nombre || 'Usuario'} ${
        user.apellido || ''
      }`,
      'Cerrar',
      {
        duration: 3000,
        horizontalPosition: 'end',
        verticalPosition: 'top',
        panelClass: ['info-snackbar'],
      }
    );

    // Solo actualizar la tabla si estamos en la primera página
    // para evitar problemas de paginación
    if (this.currentPage === 0) {
      // Verificar si el usuario ya existe en la tabla
      const existingIndex = this.tableData.findIndex((u) => u.id === user.id);
      if (existingIndex === -1) {
        // Si no existe, añadirlo al principio de la tabla
        this.tableData = [user, ...this.tableData];

        // Si la tabla tiene más elementos que el tamaño de página, eliminar el último
        if (this.tableData.length > this.pageSize) {
          this.tableData = this.tableData.slice(0, this.pageSize);
        }
      }
    }

    // Actualizar el contador total independientemente de la página actual
    this.tableTotalItems++;
  }

  /**
   * Maneja la actualización de un usuario recibido por WebSocket
   * @param user Usuario actualizado
   */
  private handleUserUpdated(user: any): void {
    if (!user) return;

    console.log('Manejando usuario actualizado:', user);

    // Mostrar notificación con detalles específicos si se actualizó el rol
    const notificationMessage = user.role
      ? `Usuario actualizado: ${user.nombre || 'Usuario'} ${
          user.apellido || ''
        } - Rol: ${user.role}`
      : `Usuario actualizado: ${user.nombre || 'Usuario'} ${
          user.apellido || ''
        }`;

    this.snackBar.open(notificationMessage, 'Cerrar', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['info-snackbar'],
    });

    // Verificar si hay una búsqueda activa
    const hasActiveSearch = this.searchTerm && this.searchTerm.trim() !== '';

    // Actualizar el usuario en la tabla si está presente
    if (this.tableData) {
      const index = this.tableData.findIndex((u) => u.id === user.id);
      if (index !== -1) {
        // Crear una copia de la tabla para forzar la detección de cambios
        const updatedTableData = [...this.tableData];

        // Preservar la posición original del usuario en la tabla
        // y mantener cualquier propiedad específica de la UI que no venga en la actualización
        const updatedUser = {
          ...updatedTableData[index], // Mantener propiedades existentes
          ...user, // Sobrescribir con propiedades actualizadas
          // Asegurarse de que el estado online se mantenga
          online: this.isUserOnline(user.id),
        };

        updatedTableData[index] = updatedUser;
        this.tableData = updatedTableData;

        // Forzar actualización de la UI para reflejar los cambios inmediatamente
        setTimeout(() => {
          this.refreshTableData(false);
        }, 100);
      } else {
        // Si el usuario no está en la tabla actual pero debería estarlo según los filtros actuales

        // Si estamos en la primera página o hay filtros activos, considerar recargar la tabla
        if (this.currentPage === 0 || this.sedeSeleccionada) {
          if (!hasActiveSearch) {
            console.log(
              'Usuario actualizado no encontrado en la tabla, recargando datos'
            );
            // Recargar la tabla manteniendo la página actual
            this.onTableRefresh(true);
          } else {
            console.log(
              'Usuario actualizado no encontrado en la tabla con búsqueda activa, manteniendo paginación actual'
            );
          }
        } else if (!hasActiveSearch) {
          // Si no estamos en la primera página y no hay búsqueda activa,
          // el usuario podría estar en otra página, no hacemos nada para no afectar la navegación
          console.log(
            'Usuario actualizado no encontrado en la página actual, podría estar en otra página'
          );
        }
      }
    }
  }

  /**
   * Maneja la eliminación de un usuario recibido por WebSocket
   * @param user Usuario eliminado
   */
  private handleUserDeleted(user: any): void {
    if (!user) return;

    console.log('Manejando usuario eliminado:', user);

    // Verificar si hay una búsqueda activa
    const hasActiveSearch = this.searchTerm && this.searchTerm.trim() !== '';

    // Determinar si es una desactivación o eliminación definitiva
    const isInactive = user.estado === 'I';
    const notificationMessage = isInactive
      ? `Usuario desactivado: ${user.nombre || 'Usuario'} ${
          user.apellido || ''
        }`
      : `Usuario eliminado definitivamente: ${user.nombre || 'Usuario'} ${
          user.apellido || ''
        }`;

    // Mostrar notificación
    this.snackBar.open(notificationMessage, 'Cerrar', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['warning-snackbar'],
    });

    // Actualizar el usuario en la tabla si está presente
    if (this.tableData) {
      // Verificar si el usuario está en la tabla actual
      const index = this.tableData.findIndex((u) => u.id === user.id);
      if (index !== -1) {
        if (isInactive) {
          // Si es una desactivación, actualizar el estado del usuario a inactivo
          const updatedTableData = [...this.tableData];
          updatedTableData[index] = {
            ...updatedTableData[index],
            estado: 'I',
          };
          this.tableData = updatedTableData;

          // Forzar actualización de la UI para reflejar el cambio de estado
          // sin recargar datos si hay búsqueda activa
          setTimeout(() => {
            this.refreshTableData(false);
          }, 100);
        } else {
          // Si es una eliminación definitiva, quitar de la tabla
          this.tableData = this.tableData.filter((u) => u.id !== user.id);

          // Si estamos en una página que no es la primera, la tabla queda vacía,
          // y NO hay búsqueda activa, solicitar la página anterior
          if (
            this.currentPage > 0 &&
            this.tableData.length === 0 &&
            !hasActiveSearch
          ) {
            console.log(
              'Tabla vacía después de eliminar usuario, solicitando página anterior'
            );
            this.currentPage--;
            // Solicitar la página anterior manteniendo la paginación
            this.requestUsersViaWebSocket(true);
          }
          // Si hay búsqueda activa y la tabla queda vacía, mantener la página actual
          // pero mostrar un mensaje indicando que no hay resultados
          else if (hasActiveSearch && this.tableData.length === 0) {
            console.log(
              'Tabla vacía después de eliminar usuario con búsqueda activa, manteniendo página actual'
            );
          }
        }
      }

      // Actualizar el contador total si es una eliminación definitiva
      if (!isInactive) {
        this.tableTotalItems = Math.max(0, this.tableTotalItems - 1);
      }
    }
  }

  // Limpiar suscripciones al destruir el componente
  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.searchSubject.complete();
  }

  /**
   * Devuelve el valor mínimo entre dos números
   * Reemplaza Math.min para usar en la plantilla
   */
  getMinValue(a: number, b: number): number {
    return Math.min(a, b);
  }

  /**
   * Calcula el índice de la última página
   * Reemplaza Math.ceil para usar en la plantilla
   */
  getLastPageIndex(): number {
    return Math.ceil(this.tableTotalItems / this.pageSize) - 1;
  }

  /**
   * Genera un array con los números de página para la paginación
   * Incluye elipsis (...) para páginas no mostradas
   */
  getPaginationRange(): (number | string)[] {
    const totalPages = Math.ceil(this.tableTotalItems / this.pageSize);
    const currentPage = this.currentPage + 1; // Convertir a 1-based para la UI
    const range: (number | string)[] = [];

    // Siempre mostrar la primera página
    range.push(1);

    // Lógica para mostrar páginas alrededor de la página actual
    if (currentPage > 3) {
      range.push('...');
    }

    // Páginas alrededor de la actual
    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      if (i > 1 && i < totalPages) {
        range.push(i);
      }
    }

    // Elipsis antes de la última página si hay muchas páginas
    if (currentPage < totalPages - 2) {
      range.push('...');
    }

    // Siempre mostrar la última página si hay más de una página
    if (totalPages > 1) {
      range.push(totalPages);
    }

    return range;
  }

  /**
   * Convierte un número de página (1-based) a índice de página (0-based)
   * Maneja correctamente los valores string (elipsis)
   */
  getPageIndex(page: number | string): number {
    // Si es un string (elipsis), devolver el índice actual
    if (typeof page === 'string') {
      return this.currentPage;
    }
    // Si es un número, convertir de 1-based (UI) a 0-based (lógica)
    return page - 1;
  }
}
